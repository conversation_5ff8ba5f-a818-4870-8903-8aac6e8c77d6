import { apiClient, ApiResponse } from './client';

// User types
export interface User {
  id: string;
  email: string;
  name: string;
  role: 'PATIENT' | 'PROVIDER' | 'ADMIN';
  avatar?: string;
  phone?: string;
  dateOfBirth?: string;
  address?: string;
  createdAt: string;
  updatedAt: string;
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterData {
  email: string;
  password: string;
  name: string;
  role?: 'PATIENT' | 'PROVIDER';
  phone?: string;
  dateOfBirth?: string;
  address?: string;
  additionalInfo?: any;
}

export interface AuthResponse {
  user: User;
  token: string;
  refreshToken?: string;
}

export interface ChangePasswordData {
  currentPassword: string;
  newPassword: string;
}

export interface ResetPasswordData {
  email: string;
}

export interface ConfirmResetPasswordData {
  token: string;
  newPassword: string;
}

// Auth API
export const authApi = {
  // Login
  async login(credentials: LoginCredentials): Promise<ApiResponse<AuthResponse>> {
    return apiClient.post<AuthResponse>('/auth/login', credentials);
  },

  // Register
  async register(data: RegisterData): Promise<ApiResponse<AuthResponse>> {
    return apiClient.post<AuthResponse>('/auth/register', data);
  },

  // Logout
  async logout(): Promise<ApiResponse<void>> {
    return apiClient.post<void>('/auth/logout');
  },

  // Get current user profile
  async getProfile(): Promise<ApiResponse<User>> {
    return apiClient.get<User>('/auth/profile');
  },

  // Update profile
  async updateProfile(data: Partial<User>): Promise<ApiResponse<User>> {
    return apiClient.put<User>('/auth/profile', data);
  },

  // Change password
  async changePassword(data: ChangePasswordData): Promise<ApiResponse<void>> {
    return apiClient.put<void>('/auth/change-password', data);
  },

  // Request password reset
  async resetPassword(data: ResetPasswordData): Promise<ApiResponse<void>> {
    return apiClient.post<void>('/auth/reset-password', data);
  },

  // Confirm password reset
  async confirmResetPassword(data: ConfirmResetPasswordData): Promise<ApiResponse<void>> {
    return apiClient.post<void>('/auth/confirm-reset-password', data);
  },

  // Refresh token
  async refreshToken(): Promise<ApiResponse<{ token: string; refreshToken?: string }>> {
    const refreshToken = localStorage.getItem('refresh_token');
    return apiClient.post<{ token: string; refreshToken?: string }>('/auth/refresh', {
      refreshToken
    });
  },

  // Verify email
  async verifyEmail(token: string): Promise<ApiResponse<void>> {
    return apiClient.post<void>('/auth/verify-email', { token });
  },

  // Resend verification email
  async resendVerification(): Promise<ApiResponse<void>> {
    return apiClient.post<void>('/auth/resend-verification');
  },

  // Verify token
  async verifyToken(): Promise<ApiResponse<{ valid: boolean; user?: User }>> {
    return apiClient.get<{ valid: boolean; user?: User }>('/auth/verify-token');
  },
};

export default authApi;
