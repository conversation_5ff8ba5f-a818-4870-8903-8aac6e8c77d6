
import { createRoot } from 'react-dom/client'

// Absolute minimal test
const root = document.getElementById("root");
if (root) {
  createRoot(root).render(
    <div style={{ padding: '20px', fontSize: '24px', color: 'red' }}>
      <h1>MINIMAL TEST - React is working!</h1>
      <p>If you see this, <PERSON>act is rendering.</p>
    </div>
  );
} else {
  document.body.innerHTML = '<h1 style="color: red;">ROOT ELEMENT NOT FOUND!</h1>';
}
