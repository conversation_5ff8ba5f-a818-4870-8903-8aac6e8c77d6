import React from 'react';
import { BrowserRouter, Routes, Route } from 'react-router-dom';
import { AuthProvider } from './lib/auth/authProvider';
import { ThemeProvider } from './components/theme/ThemeProvider';
import Index from './pages/Index';

const SimpleApp = () => {
  return (
    <AuthProvider>
      <ThemeProvider defaultTheme="light" storageKey="fintan-theme">
        <BrowserRouter>
          <Routes>
            <Route path="/" element={<Index />} />
            <Route path="*" element={
              <div style={{ padding: '20px' }}>
                <h1>Page Not Found</h1>
                <p>The page you're looking for doesn't exist.</p>
              </div>
            } />
          </Routes>
        </BrowserRouter>
      </ThemeProvider>
    </AuthProvider>
  );
};

export default SimpleApp;
