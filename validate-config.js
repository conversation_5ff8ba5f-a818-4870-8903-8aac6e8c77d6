#!/usr/bin/env node

/**
 * Configuration Validation Script
 * Validates TypeScript and Vite configuration to prevent UI breakage
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// ANSI color codes
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function validateTsConfig() {
  log('\n🔍 Validating tsconfig.json...', 'blue');
  
  try {
    const tsConfigPath = path.join(process.cwd(), 'tsconfig.json');
    const tsConfig = JSON.parse(fs.readFileSync(tsConfigPath, 'utf8'));
    
    const requiredOptions = {
      'jsx': 'react-jsx',
      'allowJs': false,
      'target': 'ESNext',
      'module': 'ESNext',
      'moduleResolution': 'bundler'
    };
    
    const forbiddenOptions = [
      'allowImportingTsExtensions',
      'baseUrl',
      'exclude'
    ];
    
    let isValid = true;
    
    // Check required options
    for (const [key, expectedValue] of Object.entries(requiredOptions)) {
      const actualValue = tsConfig.compilerOptions[key];
      if (actualValue !== expectedValue) {
        log(`❌ ${key}: Expected "${expectedValue}", got "${actualValue}"`, 'red');
        isValid = false;
      } else {
        log(`✅ ${key}: ${actualValue}`, 'green');
      }
    }
    
    // Check forbidden options
    for (const option of forbiddenOptions) {
      if (tsConfig.compilerOptions[option] !== undefined) {
        log(`❌ Forbidden option found: ${option}`, 'red');
        isValid = false;
      } else {
        log(`✅ No forbidden option: ${option}`, 'green');
      }
    }
    
    // Check paths configuration
    if (tsConfig.compilerOptions.paths && tsConfig.compilerOptions.paths['@/*']) {
      log(`✅ Path alias configured: @/* -> ${tsConfig.compilerOptions.paths['@/*']}`, 'green');
    } else {
      log(`❌ Missing path alias configuration`, 'red');
      isValid = false;
    }
    
    return isValid;
    
  } catch (error) {
    log(`❌ Error reading tsconfig.json: ${error.message}`, 'red');
    return false;
  }
}

function validateViteConfig() {
  log('\n🔍 Validating vite.config.ts...', 'blue');
  
  try {
    const viteConfigPath = path.join(process.cwd(), 'vite.config.ts');
    const viteConfigContent = fs.readFileSync(viteConfigPath, 'utf8');
    
    let isValid = true;
    
    // Check for required imports
    if (viteConfigContent.includes('import { defineConfig }')) {
      log('✅ defineConfig import found', 'green');
    } else {
      log('❌ Missing defineConfig import', 'red');
      isValid = false;
    }
    
    // Check for React plugin
    if (viteConfigContent.includes('@vitejs/plugin-react-swc')) {
      log('✅ React SWC plugin configured', 'green');
    } else {
      log('❌ Missing React SWC plugin', 'red');
      isValid = false;
    }
    
    // Check for path alias
    if (viteConfigContent.includes('@": path.resolve(__dirname, "./src")')) {
      log('✅ Path alias configured in Vite', 'green');
    } else {
      log('❌ Missing path alias in Vite config', 'red');
      isValid = false;
    }
    
    // Check for port configuration
    if (viteConfigContent.includes('10000')) {
      log('✅ Port 10000 configured', 'green');
    } else {
      log('⚠️  Port 10000 not explicitly set', 'yellow');
    }
    
    return isValid;
    
  } catch (error) {
    log(`❌ Error reading vite.config.ts: ${error.message}`, 'red');
    return false;
  }
}

function validateImportPatterns() {
  log('\n🔍 Validating import patterns in source files...', 'blue');
  
  const srcDir = path.join(process.cwd(), 'src');
  let isValid = true;
  let checkedFiles = 0;
  
  function checkFile(filePath) {
    if (!filePath.endsWith('.tsx') && !filePath.endsWith('.ts')) return;
    
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      const relativePath = path.relative(process.cwd(), filePath);
      checkedFiles++;
      
      // Check for forbidden .tsx/.ts extensions in imports
      const extensionImports = content.match(/import.*from\s+['"][^'"]*\.tsx?['"];?/g);
      if (extensionImports) {
        log(`❌ ${relativePath}: Found .tsx/.ts extensions in imports:`, 'red');
        extensionImports.forEach(imp => log(`   ${imp}`, 'red'));
        isValid = false;
      }
      
      // Check for unnecessary React imports
      const reactImports = content.match(/import\s+React[,\s]/g);
      if (reactImports && content.includes('jsx')) {
        log(`⚠️  ${relativePath}: Unnecessary React import found`, 'yellow');
      }
      
    } catch (error) {
      log(`❌ Error reading ${filePath}: ${error.message}`, 'red');
    }
  }
  
  function walkDir(dir) {
    try {
      const files = fs.readdirSync(dir);
      for (const file of files) {
        const filePath = path.join(dir, file);
        const stat = fs.statSync(filePath);
        
        if (stat.isDirectory()) {
          walkDir(filePath);
        } else {
          checkFile(filePath);
        }
      }
    } catch (error) {
      log(`❌ Error walking directory ${dir}: ${error.message}`, 'red');
    }
  }
  
  if (fs.existsSync(srcDir)) {
    walkDir(srcDir);
    log(`✅ Checked ${checkedFiles} TypeScript files`, 'green');
  } else {
    log('❌ src/ directory not found', 'red');
    isValid = false;
  }
  
  return isValid;
}

function validatePackageJson() {
  log('\n🔍 Validating package.json...', 'blue');
  
  try {
    const packagePath = path.join(process.cwd(), 'package.json');
    const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
    
    let isValid = true;
    
    // Check for required dependencies
    const requiredDeps = [
      'react',
      'react-dom',
      'react-router-dom',
      'vite',
      '@vitejs/plugin-react-swc'
    ];
    
    for (const dep of requiredDeps) {
      if (packageJson.dependencies?.[dep] || packageJson.devDependencies?.[dep]) {
        log(`✅ ${dep} dependency found`, 'green');
      } else {
        log(`❌ Missing dependency: ${dep}`, 'red');
        isValid = false;
      }
    }
    
    // Check scripts
    if (packageJson.scripts?.dev) {
      log(`✅ dev script: ${packageJson.scripts.dev}`, 'green');
    } else {
      log('❌ Missing dev script', 'red');
      isValid = false;
    }
    
    return isValid;
    
  } catch (error) {
    log(`❌ Error reading package.json: ${error.message}`, 'red');
    return false;
  }
}

function main() {
  log('🚀 Dr. Fintan Virtual Care Hub - Configuration Validator', 'bold');
  log('=' .repeat(60), 'blue');
  
  const results = {
    tsConfig: validateTsConfig(),
    viteConfig: validateViteConfig(),
    imports: validateImportPatterns(),
    packageJson: validatePackageJson()
  };
  
  log('\n📊 Validation Summary:', 'bold');
  log('=' .repeat(30), 'blue');
  
  for (const [check, passed] of Object.entries(results)) {
    const status = passed ? '✅ PASS' : '❌ FAIL';
    const color = passed ? 'green' : 'red';
    log(`${check.padEnd(15)}: ${status}`, color);
  }
  
  const allPassed = Object.values(results).every(Boolean);
  
  if (allPassed) {
    log('\n🎉 All validations passed! Configuration is correct.', 'green');
    log('✅ Frontend should be working at http://localhost:10000', 'green');
  } else {
    log('\n⚠️  Some validations failed. Please fix the issues above.', 'red');
    log('📖 Refer to TYPESCRIPT_CONFIGURATION_RULES.md for guidance.', 'yellow');
  }
  
  process.exit(allPassed ? 0 : 1);
}

// Run if this is the main module
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}
